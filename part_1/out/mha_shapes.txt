Input x:           (1, 5, 12) = (B,T,d_model)
Linear qkv(x):     (1, 5, 36) = (B,T,3*d_model)
view to 5D:        (1, 5, 3, 3, 4) = (B,T,3,heads,d_head)
q,k,v split:       q=(1, 5, 3, 4) k=(1, 5, 3, 4) v=(1, 5, 3, 4)
transpose heads:   q=(1, 3, 5, 4) k=(1, 3, 5, 4) v=(1, 3, 5, 4) = (B,heads,T,d_head)
scores q@k^T:      (1, 3, 5, 5) = (B,heads,T,T)
softmax(weights):  (1, 3, 5, 5) = (B,heads,T,T)
context @v:        (1, 3, 5, 4) = (B,heads,T,d_head)
merge heads:       (1, 5, 12) = (B,T,d_model)
final proj:        (1, 5, 12) = (B,T,d_model)

Legend:
  B=batch, T=sequence length, d_model=embedding size, heads=n_head, d_head=d_model/heads
  qkv(x) is a single Linear producing [Q|K|V]; we reshape then split into q,k,v
